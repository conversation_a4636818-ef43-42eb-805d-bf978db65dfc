import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';
import 'package:family_management/domain/enums/shopping_list_type.dart';

class ShoppingListRemoteDataSource extends BaseRemotedatasource {
  ShoppingListRemoteDataSource({required super.dio});

  Future<Response> shoppingLists(
      {required int familyId,
      int page = 1,
      String? groupBy,
      ShoppingListType? type}) async {
    String url =
        'shopping-lists?family_id=$familyId&page=$page&type=${type?.value()}';
    if (groupBy != null && groupBy.isNotEmpty) {
      url += '&group_by=$groupBy';
    }
    return await get(endpoint: url);
  }

  Future<Response> deleteShoppingList(int id) async {
    return await delete(endpoint: 'shopping-lists/$id');
  }

  Future<Response> createShoppingList(
      {required int familyId,
      required String name,
      List<String>? items,
      required type}) async {
    Map<String, dynamic> data = {
      'family_id': familyId,
      'name': name,
      'type': type,
      if (items != null && items.isNotEmpty) 'items': items,
    };

    return await post(endpoint: 'shopping-lists', data: data);
  }

  Future<Response> checkItem(int id) async {
    return await post(endpoint: 'shopping-lists/check/$id', data: {});
  }
}
