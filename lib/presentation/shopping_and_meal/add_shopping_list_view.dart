import 'package:family_management/domain/controllers/add_shopping_and_meal_controller.dart';
import 'package:family_management/domain/enums/shopping_list_type.dart';
import 'package:family_management/presentation/widget/app_bar.dart';
import 'package:family_management/presentation/widget/custom_dropdown.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../widget/full_width_button.dart';
import '../widget/text_input.dart';

class AddShoppingListView extends GetView<AddShoppingAndMealController> {
  const AddShoppingListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          appBar: mainAppBar(
              context: context,
              title: 'Add Shopping List'.tr,
              withBackButton: true,
              withDrawer: false),
          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 30),
                  Row(
                    children: [
                      SvgPicture.asset(
                        'assets/svgs/create_meal.svg',
                      ),
                      const SizedBox(width: 10),
                      Text(
                        'Create Shopping List: '.tr,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: TextInput(
                      controller: controller.nameController,
                      hint: '+ Add shopping list name'.tr,
                      minLines: 1,
                      maxLines: 1,
                      prefixIcon: SvgPicture.asset(
                        'assets/svgs/create_meal.svg',
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                  CustomDropdown(
                      value: controller.type,
                      onChanged: (value) => controller.type = value!,
                      items: [
                        DropdownMenuItem(
                          value:
                              ShoppingListType.shoppingList.value().toString(),
                          child: TextWidget(
                            text: ShoppingListType.shoppingList.toString().tr,
                          ),
                        ),
                        DropdownMenuItem(
                          value: ShoppingListType.mealList.value().toString(),
                          child: TextWidget(
                            text: ShoppingListType.mealList.toString().tr,
                          ),
                        ),
                      ]),
                  const SizedBox(height: 30),
                  TextWidget(
                    text: 'Shopping List Items'.tr,
                    fontWeight: FontWeight.bold,
                  ),
                  const SizedBox(height: 10),
                  Column(
                    children: List.generate(
                      controller.itemsController.length,
                      (index) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextInput(
                                controller: controller.itemsController[index],
                                hint: 'Enter item name'.tr,
                                minLines: 1,
                                maxLines: 1,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.remove_circle,
                                  color: Colors.red),
                              onPressed: () =>
                                  controller.removeShoppingItem(index),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: TextButton.icon(
                      icon: const Icon(Icons.add),
                      label: TextWidget(text: 'Add Item'.tr),
                      onPressed: () => controller.addShoppingItem(),
                    ),
                  ),
                  const SizedBox(height: 30),
                  FullWidthButton(
                    loading: controller.loading,
                    text: 'Confirm'.tr,
                    onPressed: () {
                      controller.createShoppingList();
                      //Get.toNamed(AppRoutes.doneMeal);
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
