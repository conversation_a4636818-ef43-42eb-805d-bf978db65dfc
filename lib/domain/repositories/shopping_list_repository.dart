import 'package:family_management/domain/enums/shopping_list_type.dart';
import 'package:family_management/domain/models/shopping_list.dart' as model;
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/utils/failure.dart';
import 'package:family_management/utils/utils.dart';
import 'package:fpdart/fpdart.dart';

import '../../data/datasources/remote_datasources/shopping_list_remotedatasource.dart';

class ShoppingListRepository extends BaseRepository {
  final ShoppingListRemoteDataSource remoteDataSource;

  ShoppingListRepository(
      {required this.remoteDataSource, required super.baseRemotedatasource});

  Future<Either<Failure, List<model.ShoppingList>>> getShoppingLists(
      {required int familyId,
      int page = 1,
      String? groupBy,
      ShoppingListType? type}) async {
    try {
      final response = await remoteDataSource.shoppingLists(
          familyId: familyId, page: page, groupBy: groupBy, type: type);
      List<model.ShoppingList> shoppingLists = [];
      for (final item in response.data['data']) {
        shoppingLists.add(model.ShoppingList.fromJson(item));
      }
      return Right(shoppingLists);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> deleteShoppingList(int id) async {
    try {
      await remoteDataSource.deleteShoppingList(id);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, model.ShoppingList>> createShoppingList({
    required int familyId,
    required String name,
    required type,
    List<String>? items,
  }) async {
    try {
      final response = await remoteDataSource.createShoppingList(
          familyId: familyId, name: name, items: items, type: type);

      final shoppingList = model.ShoppingList.fromJson(response.data['data']);
      return Right(shoppingList);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> checkItem(int id) async {
    try {
      await remoteDataSource.checkItem(id);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }
}
