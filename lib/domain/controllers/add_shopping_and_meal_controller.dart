import 'package:family_management/domain/controllers/shopping_and_meal_controller.dart';
import 'package:family_management/domain/enums/shopping_list_type.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../app_service.dart';
import '../../injections.dart';
import '../../utils/utils.dart';
import '../repositories/shopping_list_repository.dart';

class AddShoppingAndMealController extends GetxController {
  final appService = Get.find<AppService>();
  final RxBool _loading = false.obs;
  final RxList<TextEditingController> itemsController =
      <TextEditingController>[].obs;
  final ShoppingListRepository shoppingListRepository =
      gt<ShoppingListRepository>();
  final nameController = TextEditingController();

  final RxString _type = ShoppingListType.shoppingList.value().toString().obs;

  bool get loading => _loading.value;
  String get type => _type.value;

  set type(String value) => _type.value = value;

  Future<void> createShoppingList() async {
    _loading.value = true;
    final List<String> items = itemsController
        .map((controller) => controller.text.trim())
        .where((item) => item.isNotEmpty)
        .toList();

    final result = await shoppingListRepository.createShoppingList(
        familyId: appService.user.families.first.id!,
        name: nameController.text,
        items: items,
        type: type);

    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        Utils.showToast("Shopping List created successfully".tr);
        final shoppingAndMealController = Get.find<ShoppingAndMealController>();
        shoppingAndMealController.getShoppingLists();
        shoppingAndMealController.getMeals();
        Get.back();
      },
    );
    _loading.value = false;
  }

  void addShoppingItem() {
    itemsController.add(TextEditingController());
  }

  void removeShoppingItem(int index) {
    itemsController[index].dispose();
    itemsController.removeAt(index);
  }

  void clearInputs() {
    nameController.clear();
    for (var controller in itemsController) {
      controller.dispose();
    }
    itemsController.clear();
  }
}
